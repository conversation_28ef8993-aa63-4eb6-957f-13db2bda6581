﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;


namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// Professional cosigning component for document signature workflow
    /// Designed with Nabla-inspired UI/UX principles
    /// </summary>
    public partial class CosigningComponent
    {
        /// <summary>
        /// Current active user for authentication context
        /// </summary>
        [Inject] private ActiveUser CurrentUser { get; set; }
        [Inject] UserContext UserContext { get; set; }

        /// <summary>
        /// Navigation manager for page navigation
        /// </summary>
        [Inject] private NavigationManager NavigationManager { get; set; } 

        /// <summary>
        /// Member service for getting provider list
        /// </summary>
        [Inject] private IMemberService MemberService { get; set; }

        /// <summary>
        /// Cosigning request service for review request workflow
        /// </summary>
        [Inject] private ICosigningRequestService CosigningRequestService { get; set; }

        /// <summary>
        /// The record ID for which cosigning is being performed
        /// </summary>
        [Parameter] public Guid RecordId { get; set; }

        /// <summary>
        /// The patient ID associated with the record
        /// </summary>
        [Parameter] public Guid PatientId { get; set; }

        /// <summary>
        /// The patient name to display in the component
        /// </summary>
        [Parameter] public string PatientName { get; set; } = string.Empty;

        /// <summary>
        /// The organization ID for the cosigning workflow
        /// </summary>
        [Parameter] public Guid OrganizationId { get; set; }

        /// <summary>
        /// Whether to show the cosigning section
        /// </summary>
        [Parameter] public bool ShowCosigningSection { get; set; } = true;

        /// <summary>
        /// Whether the document requires a cosignature
        /// </summary>
        [Parameter] public bool RequiresCosignature { get; set; } = false;

        /// <summary>
        /// Event callback fired when signature is updated
        /// </summary>
        [Parameter] public EventCallback<Cosigning> OnSignatureUpdated { get; set; }

        /// <summary>
        /// Current cosigning state
        /// </summary>
        private Cosigning CurrentCosigning { get; set; } = new();

        /// <summary>
        /// Flag to track if the current cosigning record exists in the database
        /// </summary>
        private bool IsNewRecord { get; set; } = true;

        /// <summary>
        /// Whether a signing operation is in progress
        /// </summary>
        private bool IsProcessing { get; set; } = false;
        private bool Subscription { get; set; } = false;

        // Inline signing form properties
        private bool _showSigningForm = false;
        private List<Member> _providerList = new List<Member>();

        // Review request properties
        private CosigningRequest? ActiveReviewRequest = null;
        private bool _showRequestReviewDialog = false;
        private Member? _selectedReviewProvider = null;
        private string _reviewRequestComment = string.Empty;
        private readonly DialogOptions _dialogOptions = new()
        {
            CloseOnEscapeKey = true,
            MaxWidth = MaxWidth.Small,
            FullWidth = true
        };

        /// <summary>
        /// Initialize the component and load cosigning status
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadCosigningStatus();
                await LoadReviewRequestStatus();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing cosigning component for record {RecordId}", RecordId);
            }
        }

        /// <summary>
        /// Handle parameter changes and reload cosigning status if needed
        /// </summary>
        protected override async Task OnParametersSetAsync()
        {
            if (RecordId != Guid.Empty)
            {
                await LoadCosigningStatus();
            }
        }

        /// <summary>
        /// Load the current cosigning status from the backend
        /// </summary>
        private async Task LoadCosigningStatus()
        {
            try
            {
                Logger.LogInformation("Loading cosigning status for record {RecordId}", RecordId);

                var cosignings = await CosigningService.GetCosigningsByRecordId(RecordId, UserContext.ActiveUserOrganizationID, Subscription);
                var existingCosigning = cosignings?.OrderByDescending(c => c.Date).FirstOrDefault();

                if (existingCosigning != null)
                {
                    CurrentCosigning = existingCosigning;
                    IsProcessing = CurrentCosigning.IsLocked;
                    IsNewRecord = false;
                    Logger.LogInformation("Found existing cosigning record for {RecordId}. IsSigned: {IsSigned}, IsCosigned: {IsCosigned}",
                        RecordId, CurrentCosigning.IsSigned, CurrentCosigning.IsCosigned);
                }
                else
                {
                    // Initialize with default values - don't assign ID yet
                    CurrentCosigning = new Cosigning
                    {
                        RecordId = RecordId,
                        OrganizationId = UserContext.ActiveUserOrganizationID,
                        IsSigned = false,
                        IsCosigned = false,
                        IsLocked = false
                    };
                    IsNewRecord = true;
                    Logger.LogInformation("No existing cosigning record found for {RecordId}. Initialized new record.", RecordId);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading cosigning status for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorLoadingSignatureStatus"], Severity.Error);

                // Initialize with default values on error
                CurrentCosigning = new Cosigning
                {
                    RecordId = RecordId,
                    OrganizationId = UserContext.ActiveUserOrganizationID,
                    IsSigned = false,
                    IsCosigned = false,
                    IsLocked = false
                };
                IsNewRecord = true;
            }
        }



        /// <summary>
        /// Get the appropriate color for the status chip
        /// </summary>
        private Color GetStatusColor()
        {
            if (CurrentCosigning.IsLocked) return Color.Warning;
            if (CurrentCosigning.IsCosigned) return Color.Success;
            if (CurrentCosigning.IsSigned) return Color.Info;
            return Color.Default;
        }

        /// <summary>
        /// Get the status text for the status chip
        /// </summary>
        private string GetStatusText()
        {
            if (CurrentCosigning.IsLocked) return Localizer["Locked"];
            if (CurrentCosigning.IsCosigned) return Localizer["Cosigned"];
            if (CurrentCosigning.IsSigned) return Localizer["Signed"];
            return Localizer["PendingSignature"];
        }

        /// <summary>
        /// Public method to refresh the signature status
        /// Can be called from parent components
        /// </summary>
        public async Task RefreshSignatureStatus()
        {
            await LoadCosigningStatus();
            await LoadReviewRequestStatus();
            StateHasChanged();
        }

        /// <summary>
        /// Check if the component should be visible based on current state
        /// </summary>
        public bool ShouldShowComponent()
        {
            return ShowCosigningSection && RecordId != Guid.Empty;
        }

        /// <summary>
        /// Get the current signature completion percentage for progress indicators
        /// </summary>
        public int GetSignatureProgress()
        {
            if (CurrentCosigning.IsLocked) return 100;
            if (CurrentCosigning.IsCosigned) return 100;
            if (CurrentCosigning.IsSigned && !RequiresCosignature) return 100;
            if (CurrentCosigning.IsSigned && RequiresCosignature) return 75;
            return 0;
        }

        /// <summary>
        /// Show the inline signing form
        /// </summary>
        private async Task ShowSigningForm()
        {
            try
            {
                // Don't show form if document is locked
                if (CurrentCosigning.IsLocked)
                {
                    Snackbar.Add(Localizer["DocumentIsLocked"], Severity.Warning);
                    return;
                }

                // Load provider list for cosigning
                await LoadProviderList();

                // Reset form state
                _showSigningForm = true;

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error showing signing form for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorShowingSigningForm"], Severity.Error);
            }
        }

        /// <summary>
        /// Cancel the signing process
        /// </summary>
        private void CancelSigning()
        {
            _showSigningForm = false;
            StateHasChanged();
        }

        /// <summary>
        /// Process the signature (sign or cosign)
        /// </summary>
        private async Task ProcessSignature()
        {
            try
            {
                // Don't process if document is locked
                if (CurrentCosigning.IsLocked)
                {
                    Snackbar.Add(Localizer["DocumentIsLocked"], Severity.Warning);
                    _showSigningForm = false;
                    return;
                }

                // Validate user information
                if (!Guid.TryParse(CurrentUser.id, out var userId) || userId == Guid.Empty)
                {
                    Logger.LogError("Invalid user ID: {UserId}", CurrentUser.id);
                    Snackbar.Add(Localizer["InvalidUserForSigning"], Severity.Error);
                    return;
                }

                var userName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User";
                if (string.IsNullOrWhiteSpace(userName) || userName == "Unknown User")
                {
                    Logger.LogError("Invalid user name: {UserName}", userName);
                    Snackbar.Add(Localizer["InvalidUserNameForSigning"], Severity.Error);
                    return;
                }

                var now = DateTime.UtcNow;

                // Process signing only
                CurrentCosigning.IsSigned = true;
                CurrentCosigning.SignerId = userId;
                CurrentCosigning.SignerName = userName;
                CurrentCosigning.Date = now;
                CurrentCosigning.LastUpdated = now;
                CurrentCosigning.OrganizationId = OrganizationId;

                // Reset cosigning fields when re-signing
                CurrentCosigning.IsCosigned = false;
                CurrentCosigning.CosignerId = Guid.Empty;
                CurrentCosigning.CosignerName = string.Empty;

                Logger.LogInformation("Signing document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentSignedSuccessfully"], Severity.Success);

                // Save to database
                if (IsNewRecord)
                {
                    CurrentCosigning.Id = Guid.NewGuid();
                    await CosigningService.AddCosigning(new List<Cosigning> { CurrentCosigning });
                    IsNewRecord = false;
                }
                else
                {
                    await CosigningService.UpdateCosigning(CurrentCosigning);
                }

                // Close form and refresh
                _showSigningForm = false;
                await LoadCosigningStatus(); // Reload from database to ensure we have latest state
                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing signature for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorProcessingSignature"], Severity.Error);
                _showSigningForm = false;
            }
        }



        /// <summary>
        /// Lock the document
        /// </summary>
        private async Task LockDocument()
        {

            try
            {
                Logger.LogInformation("Locking document for record {RecordId}", RecordId);

                CurrentCosigning.IsLocked = true;
                CurrentCosigning.LastUpdated = DateTime.UtcNow;

                await CosigningService.UpdateCosigning(CurrentCosigning);

                Logger.LogInformation("Document locked successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentLockedSuccessfully"], Severity.Success);

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error locking document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorLockingDocument"], Severity.Error);
            }

        }

        /// <summary>
        /// Get the signature text for display in the primary signature field
        /// </summary>
        private string GetSignatureText()
        {
            var lines = new List<string>();

            if (CurrentCosigning.IsSigned)
            {
                lines.Add($"Electronically Signed by {CurrentCosigning.SignerName} on {CurrentCosigning.Date:MM/dd/yyyy}");
            }

            if (CurrentCosigning.IsCosigned)
            {
                lines.Add($"Electronically Cosigned by {CurrentCosigning.CosignerName} on {CurrentCosigning.LastUpdated:MM/dd/yyyy}");
            }

            if (CurrentCosigning.IsLocked)
            {
                lines.Add("Notes Locked!!");
            }

            return string.Join("\n", lines);
        }

        /// <summary>
        /// Get the appropriate text for the sign button based on current state
        /// </summary>
        private string GetSignButtonText()
        {
            if (CurrentCosigning.IsSigned && CurrentCosigning.IsCosigned)
            {
                return Localizer["Re-Sign/CoSign"];
            }
            else if (CurrentCosigning.IsSigned)
            {
                return Localizer["Re-Sign/CoSign"];
            }
            else
            {
                return Localizer["Sign"];
            }
        }

        /// <summary>
        /// Load the provider list for cosigning
        /// </summary>
        private async Task LoadProviderList()
        {
            try
            {
                // Clear existing list
                _providerList.Clear();
                _selectedReviewProvider = null;

                // Get providers from service
                var providers = await MemberService.GetProviderlistAsync(OrganizationId, Subscription);

                // Convert string providers to Member objects if needed
                if (providers != null && providers.Any())
                {
                    foreach (var provider in providers)
                    {
                        // Skip current user from cosigner list
                        if (provider == CurrentUser.displayName)
                            continue;

                        _providerList.Add(new Member
                        {
                            Id = Guid.NewGuid(), // This is just a placeholder for UI selection
                            UserName = provider
                        });
                    }
                }

                Logger.LogInformation("Loaded {Count} providers for cosigning", _providerList.Count);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading provider list for organization {OrganizationId}", OrganizationId);
                Snackbar.Add(Localizer["ErrorLoadingProviders"], Severity.Error);
            }
        }

        /// <summary>
        /// Load the current review request status
        /// </summary>
        private async Task LoadReviewRequestStatus()
        {
            try
            {
                ActiveReviewRequest = await CosigningRequestService.GetActiveRequestAsync(
                    RecordId,
                    UserContext.ActiveUserOrganizationID,
                    Subscription);

                // If request is approved, automatically cosign the document
                if (ActiveReviewRequest != null && ActiveReviewRequest.Status == CosigningRequestStatus.Approved && !CurrentCosigning.IsCosigned)
                {
                    await AutoCosignDocument();
                }

                Logger.LogInformation("Review request status loaded for record {RecordId}. Active request: {HasRequest}",
                    RecordId, ActiveReviewRequest != null);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading review request status for record {RecordId}", RecordId);
                // Don't show error to user as this is not critical
            }
        }

        /// <summary>
        /// Automatically cosign the document when request is approved
        /// </summary>
        private async Task AutoCosignDocument()
        {
            try
            {
                if (ActiveReviewRequest == null || CurrentCosigning == null) return;

                // Update the cosigning record with reviewer information
                CurrentCosigning.IsCosigned = true;
                CurrentCosigning.CosignerId = ActiveReviewRequest.ReviewerId;
                CurrentCosigning.CosignerName = ActiveReviewRequest.ReviewerName;
                CurrentCosigning.LastUpdated = DateTime.UtcNow;

                await CosigningService.UpdateCosigning(CurrentCosigning);

                Logger.LogInformation("Document automatically cosigned by {ReviewerName} for record {RecordId}",
                    ActiveReviewRequest.ReviewerName, RecordId);

                Snackbar.Add(Localizer["DocumentAutoCosigned"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error auto-cosigning document for record {RecordId}", RecordId);
            }
        }

        /// <summary>
        /// Show the request review dialog
        /// </summary>
        private async Task ShowRequestReviewDialog()
        {
            try
            {
                await LoadProviderList();
                _selectedReviewProvider = null;
                _reviewRequestComment = string.Empty;
                _showRequestReviewDialog = true;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error showing request review dialog for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorShowingRequestDialog"], Severity.Error);
            }
        }

        /// <summary>
        /// Submit a review request
        /// </summary>
        private async Task SubmitReviewRequest()
        {
            try
            {
                if (_selectedReviewProvider == null)
                {
                    Snackbar.Add(Localizer["PleaseSelectProvider"], Severity.Warning);
                    return;
                }

                IsProcessing = true;

                var request = new CosigningRequest
                {
                    Id = Guid.NewGuid(),
                    RecordId = RecordId,
                    RequesterId = Guid.Parse(CurrentUser.id),
                    RequesterName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User",
                    ReviewerId = _selectedReviewProvider.Id,
                    ReviewerName = _selectedReviewProvider.UserName,
                    Status = CosigningRequestStatus.Pending,
                    RequestedDate = DateTime.UtcNow,
                    OrganizationId = UserContext.ActiveUserOrganizationID,
                    Subscription = Subscription,
                    CommentsJson = "[]"
                };

                // Add initial comment if provided
                if (!string.IsNullOrWhiteSpace(_reviewRequestComment))
                {
                    var comments = new List<CosigningComment>
                    {
                        new CosigningComment
                        {
                            Id = Guid.NewGuid(),
                            CommenterId = Guid.Parse(CurrentUser.id),
                            CommenterName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User",
                            Comment = _reviewRequestComment,
                            CommentDate = DateTime.UtcNow,
                            CommentType = "Request"
                        }
                    };
                    request.CommentsJson = CommentHelper.SetComments(comments);
                }

                await CosigningRequestService.CreateRequestAsync(request, UserContext.ActiveUserOrganizationID, Subscription);

                // Update cosigning record to indicate cosign request type
                CurrentCosigning.Type = CosigningType.CosignRequest;
                CurrentCosigning.LastUpdated = DateTime.UtcNow;
                await CosigningService.UpdateCosigning(CurrentCosigning);

                _showRequestReviewDialog = false;
                _selectedReviewProvider = null;
                _reviewRequestComment = string.Empty;
                await LoadReviewRequestStatus(); // Refresh status

                Logger.LogInformation("Review request sent for record {RecordId} to reviewer {ReviewerId}", RecordId, request.ReviewerId);
                Snackbar.Add(Localizer["ReviewRequestSent"], Severity.Success);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error submitting review request for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorSubmittingRequest"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// Cancel the active review request
        /// </summary>
        private async Task CancelReviewRequest()
        {
            try
            {
                if (ActiveReviewRequest == null) return;

                IsProcessing = true;

                await CosigningRequestService.CancelRequestAsync(
                    ActiveReviewRequest.Id,
                    OrganizationId,
                    Subscription);

                ActiveReviewRequest = null;

                Snackbar.Add(Localizer["ReviewRequestCancelled"], Severity.Info);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error cancelling review request for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorCancellingRequest"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// Resolve a comment from the review request
        /// </summary>
        private async Task ResolveComment(Guid commentId)
        {
            try
            {
                if (ActiveReviewRequest == null) return;

                IsProcessing = true;

                await CosigningRequestService.ResolveCommentAsync(
                    ActiveReviewRequest.Id,
                    commentId,
                    Guid.Parse(CurrentUser.id),
                    CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User",
                    UserContext.ActiveUserOrganizationID,
                    Subscription);

                // Refresh the review request to get updated comments
                await LoadReviewRequestStatus();

                Snackbar.Add(Localizer["CommentResolved"], Severity.Success);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error resolving comment {CommentId}", commentId);
                Snackbar.Add(Localizer["ErrorResolvingComment"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// Check if there are any unresolved comments
        /// </summary>
        private bool HasUnresolvedComments()
        {
            if (ActiveReviewRequest == null) return false;

            var comments = CommentHelper.GetComments(ActiveReviewRequest.CommentsJson);
            return comments.Any(c => !c.IsResolved);
        }

        /// <summary>
        /// Get the severity for the review request alert
        /// </summary>
        private Severity GetReviewRequestSeverity()
        {
            if (ActiveReviewRequest == null) return Severity.Info;

            return ActiveReviewRequest.Status switch
            {
                CosigningRequestStatus.Pending => Severity.Info,

                CosigningRequestStatus.Approved => Severity.Success,
                CosigningRequestStatus.ChangesRequested => Severity.Warning,
                _ => Severity.Info
            };
        }

        /// <summary>
        /// Get the icon for the review request alert
        /// </summary>
        private string GetReviewRequestIcon()
        {
            if (ActiveReviewRequest == null) return Icons.Material.Filled.Info;

            return ActiveReviewRequest.Status switch
            {
                CosigningRequestStatus.Pending => Icons.Material.Filled.Schedule,
                CosigningRequestStatus.Approved => Icons.Material.Filled.CheckCircle,
                CosigningRequestStatus.ChangesRequested => Icons.Material.Filled.Comment,
                _ => Icons.Material.Filled.Info
            };
        }

        /// <summary>
        /// Get the status text for the review request
        /// </summary>
        private string GetReviewRequestStatusText()
        {
            if (ActiveReviewRequest == null) return string.Empty;

            return ActiveReviewRequest.Status switch
            {
                CosigningRequestStatus.Pending => Localizer["ReviewRequestPending"],
                CosigningRequestStatus.Approved => Localizer["DocumentCosigned"],
                CosigningRequestStatus.ChangesRequested => Localizer["ReviewerCommented"],
                _ => ActiveReviewRequest.Status.ToString()
            };
        }
    }
}