@page "/review-requests"
@using Microsoft.Extensions.Localization
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using MudBlazor
@using Microsoft.AspNetCore.Authorization
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@attribute [Authorize]
@layout Admin
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer

<PageTitle>@Localizer["ReviewRequests"]</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-6">
    <!-- Header Section -->
    <MudStack Row AlignItems="AlignItems.Center" Spacing="3" Class="mb-6">
        <MudIcon Icon="@Icons.Material.Filled.RateReview" Size="Size.Large" Color="Color.Primary" />
        <MudStack Spacing="1">
            <MudText Typo="Typo.h4" Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-weight: 600; color: #333;">
                @Localizer["ReviewRequests"]
            </MudText>
            <MudText Typo="Typo.body1" Style="color: #666;">
                @Localizer["ReviewRequestsDescription"]
            </MudText>
        </MudStack>
    </MudStack>

    @if (_isLoading)
    {
        <MudProgressCircular Indeterminate="true" Size="Size.Large" Class="ma-4" />
    }
    else if (!_reviewRequests.Any())
    {
        <MudAlert Severity="Severity.Info" Class="ma-4">
            @Localizer["NoReviewRequests"]
        </MudAlert>
    }
    else
    {
        <MudDataGrid Items="_reviewRequests" 
                     Filterable="true" 
                     SortMode="SortMode.Multiple" 
                     Groupable="false"
                     Class="review-requests-grid"
                     Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
            <Columns>
                <PropertyColumn Property="x => x.RequesterName" Title="@Localizer["ProviderName"]" />
                <PropertyColumn Property="x => x.PatientName" Title="@Localizer["PatientName"]" />
                <PropertyColumn Property="x => x.PatientAge" Title="@Localizer["Age"]" />
                <PropertyColumn Property="x => x.PatientGender" Title="@Localizer["Gender"]" />
                <PropertyColumn Property="x => x.RequestedDate" Title="@Localizer["RequestDate"]" Format="MM/dd/yyyy HH:mm" />
                <TemplateColumn Title="@Localizer["Status"]">
                    <CellTemplate>
                        <MudChip Color="@GetStatusColor(context.Item.Status)" Size="Size.Small">
                            @GetStatusText(context.Item.Status)
                        </MudChip>
                    </CellTemplate>
                </TemplateColumn>
                <TemplateColumn Title="@Localizer["Actions"]">
                    <CellTemplate>
                        <MudButton Variant="Variant.Filled" 
                                   Color="Color.Primary" 
                                   Size="Size.Small"
                                   OnClick="@(() => ReviewRequest(context.Item))"
                                   StartIcon="@Icons.Material.Filled.RateReview">
                            @Localizer["Review"]
                        </MudButton>
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
        </MudDataGrid>
    }
</MudContainer>

<!-- Review SOAP Notes Dialog -->
<MudDialog @bind-IsVisible="_showReviewDialog" Options="_dialogOptions">
    <DialogContent>
        @if (_selectedRequest != null)
        {
            <MudText Typo="Typo.h5" Class="mb-4" Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                @Localizer["ReviewSOAPNotes"] - @_selectedRequest.PatientName
            </MudText>

            <MudGrid>
                <!-- Left Side: SOAP Notes -->
                <MudItem xs="8">
                    <MudPaper Class="pa-4" Style="height: 600px; overflow-y: auto; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                        @if (_selectedNotes != null)
                        {
                            <div class="notes-content">
                                @((MarkupString)_selectedNotes.Notes)
                            </div>
                        }
                        else if (_isLoadingNotes)
                        {
                            <MudProgressCircular Indeterminate="true" Size="Size.Medium" />
                        }
                        else
                        {
                            <MudText>@Localizer["NotesNotFound"]</MudText>
                        }
                    </MudPaper>
                </MudItem>

                <!-- Right Side: Comments and Actions -->
                <MudItem xs="4">
                    <MudPaper Class="pa-4" Style="height: 600px; display: flex; flex-direction: column;">
                        <!-- Comments Section -->
                        <MudText Typo="Typo.h6" Class="mb-3">@Localizer["Comments"]</MudText>
                        
                        <div style="flex: 1; overflow-y: auto; margin-bottom: 16px;">
                            @if (CommentHelper.GetComments(_selectedRequest.CommentsJson).Any())
                            {
                                @foreach (var comment in CommentHelper.GetComments(_selectedRequest.CommentsJson).OrderBy(c => c.CommentDate))
                                {
                                    <MudCard Class="mb-2" Elevation="1">
                                        <MudCardContent Class="pa-3">
                                            <MudText Typo="Typo.caption" Class="mb-1">
                                                @comment.CommenterName - @comment.CommentDate.ToString("MM/dd/yyyy HH:mm")
                                            </MudText>
                                            <MudText Typo="Typo.body2">@comment.Comment</MudText>
                                            @if (!comment.IsResolved && comment.CommenterId != Guid.Parse(CurrentUser.id))
                                            {
                                                <MudButton Size="Size.Small" 
                                                           Color="Color.Success" 
                                                           Variant="Variant.Text"
                                                           OnClick="@(() => ResolveComment(comment.Id))"
                                                           Class="mt-2">
                                                    @Localizer["Resolve"]
                                                </MudButton>
                                            }
                                        </MudCardContent>
                                    </MudCard>
                                }
                            }
                            else
                            {
                                <MudText Typo="Typo.body2" Class="text-center">@Localizer["NoComments"]</MudText>
                            }
                        </div>

                        <!-- Comment Input -->
                        <MudTextField @bind-Value="_newComment" 
                                      Label="@Localizer["AddComment"]" 
                                      Multiline="true" 
                                      Lines="3" 
                                      Variant="Variant.Outlined"
                                      Class="mb-3" />

                        <!-- Action Buttons -->
                        <MudStack Row Justify="Justify.SpaceBetween">
                            <MudButton Variant="Variant.Outlined" 
                                       Color="Color.Secondary"
                                       OnClick="CloseReviewDialog"
                                       Disabled="_isProcessing">
                                @Localizer["Cancel"]
                            </MudButton>
                            
                            <MudStack Row Spacing="2">
                                <MudButton Variant="Variant.Filled" 
                                           Color="Color.Warning"
                                           OnClick="RequestChanges"
                                           Disabled="_isProcessing || string.IsNullOrWhiteSpace(_newComment)"
                                           StartIcon="@Icons.Material.Filled.Comment">
                                    @Localizer["RequestChanges"]
                                </MudButton>
                                
                                <MudButton Variant="Variant.Filled" 
                                           Color="Color.Success"
                                           OnClick="ApproveRequest"
                                           Disabled="_isProcessing"
                                           StartIcon="@Icons.Material.Filled.CheckCircle">
                                    @Localizer["Approve"]
                                </MudButton>
                            </MudStack>
                        </MudStack>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        }
    </DialogContent>
</MudDialog>

<style>
    .review-requests-grid {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .notes-content {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
    }
    
    .notes-content h1, .notes-content h2, .notes-content h3, .notes-content h4, .notes-content h5, .notes-content h6 {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin-top: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .notes-content p {
        margin-bottom: 0.75rem;
    }
</style>
