@page "/review-requests"
@using Microsoft.Extensions.Localization
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using MudBlazor
@using Microsoft.AspNetCore.Authorization
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@attribute [Authorize]
@layout Admin

<PageTitle>@Localizer["ReviewRequests"]</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-6">
    <!-- Header Section -->
    <MudStack Row AlignItems="AlignItems.Center" Spacing="3" Class="mb-6">
        <MudIcon Icon="@Icons.Material.Filled.RateReview" Size="Size.Large" Color="Color.Primary" />
        <MudStack Spacing="1">
            <MudText Typo="Typo.h4" Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-weight: 600; color: #333;">
                @Localizer["ReviewRequests"]
            </MudText>
            <MudText Typo="Typo.body1" Style="color: #666;">
                @Localizer["ReviewRequestsDescription"]
            </MudText>
        </MudStack>
    </MudStack>

    @if (_isLoading)
    {
        <MudProgressCircular Indeterminate="true" Size="Size.Large" Class="ma-4" />
    }
    else if (!_reviewRequests.Any())
    {
        <MudAlert Severity="Severity.Info" Class="ma-4">
            @Localizer["NoReviewRequests"]
        </MudAlert>
    }
    else
    {
        <MudDataGrid Items="_reviewRequests" 
                     Filterable="true" 
                     SortMode="SortMode.Multiple" 
                     Groupable="false"
                     Class="review-requests-grid"
                     Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
            <Columns>
                <PropertyColumn Property="x => x.RequesterName" Title="@Localizer["ProviderName"]" />
                <PropertyColumn Property="x => x.PatientName" Title="@Localizer["PatientName"]" />
                <PropertyColumn Property="x => x.PatientAge" Title="@Localizer["Age"]" />
                <PropertyColumn Property="x => x.PatientGender" Title="@Localizer["Gender"]" />
                <PropertyColumn Property="x => x.RequestedDate" Title="@Localizer["RequestDate"]" Format="MM/dd/yyyy HH:mm" />
                <TemplateColumn Title="@Localizer["Status"]">
                    <CellTemplate>
                        <MudChip Color="@GetStatusColor(context.Item.Status)"
                                 Size="Size.Small"
                                 Variant="Variant.Filled"
                                 Style="font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">
                            @GetStatusText(context.Item.Status)
                        </MudChip>
                    </CellTemplate>
                </TemplateColumn>
                <TemplateColumn Title="@Localizer["Actions"]">
                    <CellTemplate>
                        <MudStack Row Spacing="1" AlignItems="AlignItems.Center">
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       Size="Size.Small"
                                       OnClick="@(() => ReviewRequest(context.Item))"
                                       StartIcon="@Icons.Material.Filled.RateReview"
                                       Style="font-weight: 600;">
                                @Localizer["Review"]
                            </MudButton>
                            @if (context.Item.Status == CosigningRequestStatus.Pending)
                            {
                                <MudChip T="string" Color="Color.Warning" Size="Size.Small" Icon="@Icons.Material.Filled.Schedule">
                                    @Localizer["NewRequest"]
                                </MudChip>
                            }
                        </MudStack>
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
        </MudDataGrid>
    }
</MudContainer>

<!-- Review SOAP Notes Dialog -->
<MudDialog @bind-IsVisible="_showReviewDialog" Options="_dialogOptions">
    <TitleContent>
        <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
            <MudIcon Icon="@Icons.Material.Filled.RateReview" Color="Color.Primary" Size="Size.Large" />
            <MudStack Spacing="1">
                <MudText Typo="Typo.h5" Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-weight: 600; margin: 0;">
                    @Localizer["ReviewSOAPNotes"]
                </MudText>
                @if (_selectedRequest != null)
                {
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudChip T="string" Color="Color.Info" Size="Size.Small" Icon="@Icons.Material.Filled.Person">
                            @_selectedRequest.PatientName
                        </MudChip>
                        <MudChip T="string" Color="Color.Secondary" Size="Size.Small" Icon="@Icons.Material.Filled.CalendarToday">
                            @_selectedRequest.RequestedDate.ToString("MMM dd, yyyy")
                        </MudChip>
                        <MudChip T="string" Color="Color.Primary" Size="Size.Small" Icon="@Icons.Material.Filled.Person">
                            @Localizer["From"]: @_selectedRequest.RequesterName
                        </MudChip>
                    </MudStack>
                }
            </MudStack>
        </MudStack>
    </TitleContent>
    <DialogContent>
        @if (_selectedRequest != null)
        {
            <MudGrid Style="height: 70vh;">
                <!-- Left Side: SOAP Notes -->
                <MudItem xs="8">
                    <MudPaper Class="pa-4" Style="height: 100%; overflow-y: auto; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; border: 1px solid #e0e0e0; border-radius: 8px;">
                        @if (_selectedNotes != null)
                        {
                            <div class="notes-content">
                                @((MarkupString)_selectedNotes.Notes)
                            </div>
                        }
                        else if (_isLoadingNotes)
                        {
                            <MudStack AlignItems="AlignItems.Center" Justify="Justify.Center" Style="height: 100%;">
                                <MudProgressCircular Indeterminate="true" Size="Size.Large" Color="Color.Primary" />
                                <MudText Typo="Typo.body1" Style="margin-top: 16px;">@Localizer["LoadingNotes"]</MudText>
                            </MudStack>
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Warning" Style="height: 100%; display: flex; align-items: center; justify-content: center;">
                                <MudStack AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.Warning" Size="Size.Large" />
                                    <MudText Typo="Typo.body1">@Localizer["NotesNotFound"]</MudText>
                                </MudStack>
                            </MudAlert>
                        }
                    </MudPaper>
                </MudItem>

                <!-- Right Side: Comments and Actions -->
                <MudItem xs="4">
                    <MudPaper Class="pa-4" Style="height: 100%; display: flex; flex-direction: column; border: 1px solid #e0e0e0; border-radius: 8px; background: #f8f9fa;">
                        <!-- Comments Section Header -->
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2" Class="mb-3">
                            <MudIcon Icon="@Icons.Material.Filled.Comment" Color="Color.Primary" Size="Size.Medium" />
                            <MudText Typo="Typo.h6" Style="font-weight: 600; color: #1976d2;">@Localizer["Comments"]</MudText>
                            <MudSpacer />
                            @if (CommentHelper.GetComments(_selectedRequest.CommentsJson).Any())
                            {
                                <MudChip T="string" Color="Color.Info" Size="Size.Small">
                                    @CommentHelper.GetComments(_selectedRequest.CommentsJson).Count
                                </MudChip>
                            }
                        </MudStack>

                        <!-- Comments List -->
                        <div style="flex: 1; overflow-y: auto; margin-bottom: 16px; background: white; border-radius: 6px; padding: 8px;">
                            @if (CommentHelper.GetComments(_selectedRequest.CommentsJson).Any())
                            {
                                @foreach (var comment in CommentHelper.GetComments(_selectedRequest.CommentsJson).OrderBy(c => c.CommentDate))
                                {
                                    <div class="github-comment-style" style="border: 1px solid #e1e4e8; border-radius: 6px; background: white; margin-bottom: 12px; overflow: hidden;">
                                        <!-- Comment Header -->
                                        <div style="background: #f6f8fa; border-bottom: 1px solid #e1e4e8; padding: 8px 12px;">
                                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                <MudAvatar Size="Size.Small" Style="background: #1976d2; color: white; font-weight: 600;">
                                                    @comment.CommenterName.Substring(0, 1).ToUpper()
                                                </MudAvatar>
                                                <MudText Typo="Typo.caption" Style="font-weight: 600; color: #24292e;">
                                                    @comment.CommenterName
                                                </MudText>
                                                <MudText Typo="Typo.caption" Style="color: #586069; font-size: 0.75rem;">
                                                    @comment.CommentDate.ToString("MMM dd, HH:mm")
                                                </MudText>
                                            </MudStack>
                                        </div>

                                        <!-- Comment Body -->
                                        <div style="padding: 12px;">
                                            <MudText Typo="Typo.body2" Style="line-height: 1.5; color: #24292e; margin-bottom: 8px;">
                                                @comment.Comment
                                            </MudText>

                                            @if (!comment.IsResolved && comment.CommenterId != Guid.Parse(CurrentUser.id))
                                            {
                                                <MudButton Size="Size.Small"
                                                           Color="Color.Success"
                                                           Variant="Variant.Filled"
                                                           OnClick="@(() => ResolveComment(comment.Id))"
                                                           StartIcon="@Icons.Material.Filled.Check"
                                                           Style="border-radius: 4px; font-weight: 500;">
                                                    @Localizer["Resolve"]
                                                </MudButton>
                                            }
                                            else if (comment.IsResolved)
                                            {
                                                <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.CheckCircle">
                                                    @Localizer["Resolved"]
                                                </MudChip>
                                            }
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <MudAlert Severity="Severity.Info" Style="text-align: center; margin: 20px 0;">
                                    <MudStack AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.CommentBank" Size="Size.Large" />
                                        <MudText Typo="Typo.body2">@Localizer["NoComments"]</MudText>
                                    </MudStack>
                                </MudAlert>
                            }
                        </div>

                        <!-- Comment Input Section -->
                        <MudPaper Class="pa-3" Style="background: white; border-radius: 6px; border: 1px solid #e0e0e0;">
                            <MudStack Spacing="3">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.Edit" Color="Color.Primary" Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle2" Style="font-weight: 600; color: #1976d2;">
                                        @Localizer["AddComment"]
                                    </MudText>
                                </MudStack>

                                <MudTextField @bind-Value="_newComment"
                                              Placeholder="@Localizer["EnterYourComments"]"
                                              Multiline="true"
                                              Lines="4"
                                              Variant="Variant.Outlined"
                                              Style="background: #f8f9fa;"
                                              FullWidth="true" />

                                <!-- Action Buttons -->
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <MudButton Variant="Variant.Outlined"
                                               Color="Color.Secondary"
                                               OnClick="CloseReviewDialog"
                                               Disabled="_isProcessing"
                                               StartIcon="@Icons.Material.Filled.Close"
                                               Style="border-radius: 6px;">
                                        @Localizer["Cancel"]
                                    </MudButton>

                                    <MudStack Row Spacing="2">
                                        <MudButton Variant="Variant.Filled"
                                                   Color="Color.Warning"
                                                   OnClick="RequestChanges"
                                                   Disabled="_isProcessing || string.IsNullOrWhiteSpace(_newComment)"
                                                   StartIcon="@Icons.Material.Filled.Comment"
                                                   Style="border-radius: 6px; font-weight: 600;">
                                            @if (_isProcessing)
                                            {
                                                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                                <span style="margin-left: 8px;">@Localizer["Processing"]</span>
                                            }
                                            else
                                            {
                                                @Localizer["RequestChanges"]
                                            }
                                        </MudButton>

                                        <MudButton Variant="Variant.Filled"
                                                   Color="Color.Success"
                                                   OnClick="ApproveRequest"
                                                   Disabled="_isProcessing"
                                                   StartIcon="@Icons.Material.Filled.CheckCircle"
                                                   Style="border-radius: 6px; font-weight: 600;">
                                            @if (_isProcessing)
                                            {
                                                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                                <span style="margin-left: 8px;">@Localizer["Processing"]</span>
                                            }
                                            else
                                            {
                                                @Localizer["Approve"]
                                            }
                                        </MudButton>
                                    </MudStack>
                                </MudStack>
                            </MudStack>
                        </MudPaper>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        }
    </DialogContent>
</MudDialog>

<style>
    .review-requests-grid {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .notes-content {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #24292e;
    }

    .notes-content h1, .notes-content h2, .notes-content h3, .notes-content h4, .notes-content h5, .notes-content h6 {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
        color: #1976d2;
        font-weight: 600;
    }

    .notes-content p {
        margin-bottom: 1rem;
        text-align: justify;
    }

    .notes-content ul, .notes-content ol {
        margin-bottom: 1rem;
        padding-left: 1.5rem;
    }

    .notes-content li {
        margin-bottom: 0.5rem;
    }

    .github-comment-style {
        transition: box-shadow 0.2s ease, transform 0.1s ease;
    }

    .github-comment-style:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateY(-1px);
    }

    .mud-table-row:hover {
        background-color: #f8f9fa !important;
        transition: background-color 0.2s ease;
    }

    .mud-chip {
        font-weight: 600;
        border-radius: 6px;
    }

    .mud-button {
        text-transform: none;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .mud-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .mud-progress-circular {
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .mud-dialog {
        border-radius: 12px;
        overflow: hidden;
    }

    .mud-dialog-title {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: white;
        padding: 20px 24px;
    }

    .mud-dialog-content {
        padding: 24px;
    }

    .mud-paper {
        transition: box-shadow 0.2s ease;
    }

    .mud-paper:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
</style>
