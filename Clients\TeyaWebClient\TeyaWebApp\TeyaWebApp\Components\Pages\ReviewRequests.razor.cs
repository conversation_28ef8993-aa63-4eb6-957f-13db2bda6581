using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.TeyaAIScribeResources;

namespace TeyaWebApp.Components.Pages
{
    public partial class ReviewRequests : ComponentBase, IDisposable
    {
        [Inject] private ICosigningRequestService CosigningRequestService { get; set; }
        [Inject] private ICosigningStateService CosigningStateService { get; set; }
        [Inject] private ICosigningCommentHelper CommentHelper { get; set; }
        [Inject] private IProgressNotesService ProgressNotesService { get; set; }
        [Inject] private ActiveUser CurrentUser { get; set; }
        [Inject] private ILogger<ReviewRequests> Logger { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private NavigationManager Navigation { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        [Inject] private IStringLocalizer<TeyaAIScribeStrings> Localizer { get; set; }
        private bool Subscription = false;
        private List<CosigningRequest> _reviewRequests = new();
        private CosigningRequest? _selectedRequest;
        private Record? _selectedNotes;
        private string _newComment = string.Empty;
        private bool _isLoading = true;
        private bool _isLoadingNotes = false;
        private bool _isProcessing = false;
        private bool _showReviewDialog = false;
        private Timer? _refreshTimer;

        private readonly DialogOptions _dialogOptions = new()
        {
            MaxWidth = MaxWidth.ExtraLarge,
            FullWidth = true,
            CloseButton = true,
            
        };

        protected override async Task OnInitializedAsync()
        {
            await LoadReviewRequests();

            // Subscribe to state changes for real-time updates
            CosigningStateService.RequestApproved += OnRequestApproved;
            CosigningStateService.RequestChangesRequested += OnRequestChangesRequested;
            CosigningStateService.CommentResolved += OnCommentResolved;
            CosigningStateService.RequestCountsChanged += OnRequestCountsChanged;

            // Set up timer to refresh every 30 seconds
            _refreshTimer = new Timer(async _ => await RefreshRequests(), null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        private async Task LoadReviewRequests()
        {
            try
            {
                _isLoading = true;
                
                if (Guid.TryParse(CurrentUser.id, out var userId))
                {
                    var requests = await CosigningRequestService.GetByReviewerIdAsync(
                        userId, 
                        UserContext.ActiveUserOrganizationID, 
                        Subscription);
                    
                    _reviewRequests = requests.OrderByDescending(r => r.RequestedDate).ToList();
                    
                    // Load patient information for each request
                    foreach (var request in _reviewRequests)
                    {
                        await LoadPatientInfo(request);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading review requests");
                Snackbar.Add(Localizer["ErrorLoadingRequests"], Severity.Error);
            }
            finally
            {
                _isLoading = false;
                StateHasChanged();
            }
        }

        private async Task LoadPatientInfo(CosigningRequest request)
        {
            try
            {
                // Load the progress note to get patient information
                var notes = await ProgressNotesService.GetRecordByIdAsync(
                    request.RecordId, 
                    UserContext.ActiveUserOrganizationID, 
                    Subscription);
                
                if (notes != null)
                {
                    request.PatientName = notes.PatientName ?? "Unknown Patient";
                    request.PatientAge = CalculateAge(notes.PatientDateOfBirth).ToString();
                    request.PatientGender = notes.PatientGender ?? "Unknown";
                    request.RecordDate = notes.DateTime;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading patient info for request {RequestId}", request.Id);
                request.PatientName = "Unknown Patient";
                request.PatientAge = "Unknown";
                request.PatientGender = "Unknown";
            }
        }

        private int CalculateAge(DateTime? dateOfBirth)
        {
            if (!dateOfBirth.HasValue) return 0;
            
            var today = DateTime.Today;
            var age = today.Year - dateOfBirth.Value.Year;
            if (dateOfBirth.Value.Date > today.AddYears(-age)) age--;
            return age;
        }

        private Color GetStatusColor(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Color.Warning,
                CosigningRequestStatus.Approved => Color.Success,
                CosigningRequestStatus.ChangesRequested => Color.Error,
                _ => Color.Default
            };
        }

        private string GetStatusText(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Localizer["Pending"],
                CosigningRequestStatus.Approved => Localizer["Approved"],
                CosigningRequestStatus.ChangesRequested => Localizer["ChangesRequested"],
                _ => status.ToString()
            };
        }

        private async Task ReviewRequest(CosigningRequest request)
        {
            try
            {
                _selectedRequest = request;
                _isLoadingNotes = true;
                _showReviewDialog = true;
                StateHasChanged();

                // Load the SOAP notes
                _selectedNotes = await ProgressNotesService.GetRecordByIdAsync(
                    request.RecordId, 
                    UserContext.ActiveUserOrganizationID, 
                    Subscription);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading notes for review");
                Snackbar.Add(Localizer["ErrorLoadingNotes"], Severity.Error);
            }
            finally
            {
                _isLoadingNotes = false;
                StateHasChanged();
            }
        }

        private async Task ApproveRequest()
        {
            if (_selectedRequest == null) return;

            try
            {
                _isProcessing = true;

                await CosigningRequestService.ApproveRequestAsync(
                    _selectedRequest.Id,
                    Guid.Parse(CurrentUser.id),
                    CurrentUser.givenName,
                    UserContext.ActiveUserOrganizationID,
                   Subscription);

                Snackbar.Add(Localizer["RequestApproved"], Severity.Success);
                
                // Remove from list and close dialog
                _reviewRequests.Remove(_selectedRequest);
                CloseReviewDialog();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error approving request {RequestId}", _selectedRequest.Id);
                Snackbar.Add(Localizer["ErrorApprovingRequest"], Severity.Error);
            }
            finally
            {
                _isProcessing = false;
            }
        }

        private async Task RequestChanges()
        {
            if (_selectedRequest == null || string.IsNullOrWhiteSpace(_newComment)) return;

            try
            {
                _isProcessing = true;

                await CosigningRequestService.RequestChangesAsync(
                    _selectedRequest.Id,
                    Guid.Parse(CurrentUser.id),
                    CurrentUser.givenName,
                    _newComment,
                    UserContext.ActiveUserOrganizationID,
                    Subscription);

                Snackbar.Add(Localizer["ChangesRequested"], Severity.Success);
                
                // Update status and close dialog
                _selectedRequest.Status = CosigningRequestStatus.ChangesRequested;
                CloseReviewDialog();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error requesting changes for request {RequestId}", _selectedRequest.Id);
                Snackbar.Add(Localizer["ErrorRequestingChanges"], Severity.Error);
            }
            finally
            {
                _isProcessing = false;
            }
        }

        private async Task ResolveComment(Guid commentId)
        {
            if (_selectedRequest == null) return;

            try
            {
                await CosigningRequestService.ResolveCommentAsync(
                    _selectedRequest.Id,
                    commentId,
                    Guid.Parse(CurrentUser.id),
                    CurrentUser.givenName,
                    UserContext.ActiveUserOrganizationID,
                    Subscription);

                // Refresh the request to get updated comments
                var updatedRequest = await CosigningRequestService.GetByIdAsync(
                    _selectedRequest.Id,
                    UserContext.ActiveUserOrganizationID,
                    Subscription);

                if (updatedRequest != null)
                {
                    _selectedRequest = updatedRequest;
                    StateHasChanged();
                }

                Snackbar.Add(Localizer["CommentResolved"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error resolving comment {CommentId}", commentId);
                Snackbar.Add(Localizer["ErrorResolvingComment"], Severity.Error);
            }
        }

        private void CloseReviewDialog()
        {
            _showReviewDialog = false;
            _selectedRequest = null;
            _selectedNotes = null;
            _newComment = string.Empty;
            _isProcessing = false;
        }

        private async void OnRequestApproved(Guid requestId)
        {
            var request = _reviewRequests.FirstOrDefault(r => r.Id == requestId);
            if (request != null)
            {
                _reviewRequests.Remove(request);
                await InvokeAsync(StateHasChanged);
            }
        }

        private async void OnRequestChangesRequested(Guid requestId)
        {
            var request = _reviewRequests.FirstOrDefault(r => r.Id == requestId);
            if (request != null)
            {
                request.Status = CosigningRequestStatus.ChangesRequested;
                await InvokeAsync(StateHasChanged);
            }
        }

        private async void OnCommentResolved(Guid requestId, Guid commentId)
        {
            if (_selectedRequest?.Id == requestId)
            {
                // Refresh the selected request
                var updatedRequest = await CosigningRequestService.GetByIdAsync(
                    requestId,
                    UserContext.ActiveUserOrganizationID,
                    Subscription);

                if (updatedRequest != null)
                {
                    _selectedRequest = updatedRequest;
                    await InvokeAsync(StateHasChanged);
                }
            }
        }

        private async Task RefreshRequests()
        {
            try
            {
                await LoadReviewRequests();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error refreshing review requests");
            }
        }

        private async void OnRequestCountsChanged()
        {
            try
            {
                await RefreshRequests();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling request counts change");
            }
        }

        public void Dispose()
        {
            // Unsubscribe from events
            if (CosigningStateService != null)
            {
                CosigningStateService.RequestApproved -= OnRequestApproved;
                CosigningStateService.RequestChangesRequested -= OnRequestChangesRequested;
                CosigningStateService.CommentResolved -= OnCommentResolved;
                CosigningStateService.RequestCountsChanged -= OnRequestCountsChanged;
            }

            // Dispose timer
            _refreshTimer?.Dispose();
        }
    }
}
