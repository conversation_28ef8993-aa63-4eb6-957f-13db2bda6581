﻿@page "/ReviewSoap"
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@using Microsoft.AspNetCore.Authorization
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@attribute [Authorize]
@layout Admin
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@inject ILogger<ReviewSoap> Lo<PERSON>
@inject ISnackbar Snackbar
@inject ActiveUser CurrentUser
@inject ICosigningRequestService CosigningRequestService
@inject ICosigningCommentHelper CommentHelper
@inject IProgressNotesService ProgressNotesService


<PageTitle>@Localizer["ReviewRequests"]</PageTitle>

<div class="review-soap-container">
    <MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-6">

        <!-- Header Section -->
        <MudStack Row AlignItems="AlignItems.Center" Spacing="3" Class="mb-6">
            <MudIcon Icon="@Icons.Material.Filled.RateReview" Size="Size.Large" Color="Color.Primary" />
            <MudStack Spacing="1">
                <MudText Typo="Typo.h4" Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-weight: 600; color: #333;">
                    @Localizer["ReviewRequests"]
                </MudText>
                <MudText Typo="Typo.body1" Style="color: #666;">
                    @Localizer["ReviewRequestsDescription"]
                </MudText>
            </MudStack>
        </MudStack>

        @if (IsLoading)
        {
            <div class="d-flex justify-center pa-8">
                <MudProgressCircular Size="Size.Large" Indeterminate="true" />
            </div>
        }
        else
        {
            <MudGrid>
                <!-- Left Panel - Request List -->
                <MudItem xs="12" md="5" lg="4">
                    <MudPaper Class="pa-4" Elevation="2">
                        <div class="d-flex justify-space-between align-center mb-4">
                            <MudText Typo="Typo.h6">@Localizer["PendingRequests"]</MudText>
                            <MudChip T="string" Size="Size.Small" Color="Color.Primary">
                                @PendingRequests.Count()
                            </MudChip>
                        </div>

                        @if (!PendingRequests.Any())
                        {
                            <div class="text-center pa-8">
                                <MudIcon Icon="@Icons.Material.Outlined.CheckCircle"
                                Size="Size.Large"
                                Color="Color.Success"
                                Class="mb-4" />
                                <MudText Typo="Typo.h6" Color="Color.Success">
                                    @Localizer["NoRequestsPending"]
                                </MudText>
                                <MudText Typo="Typo.body2" Color="Color.Secondary">
                                    @Localizer["AllRequestsCompleted"]
                                </MudText>
                            </div>
                        }
                        else
                        {
                            <MudList T="CosigningRequest" Clickable="true">
                                @foreach (var request in PendingRequests)
                                {
                                    <MudListItem T="CosigningRequest" OnClick="@(() => SelectRequest(request))"
                                    Class="@(SelectedRequest?.Id == request.Id ? "selected-request" : "")"
                                    Style="border-radius: 8px; margin-bottom: 8px;">
                                        <div class="d-flex flex-column">
                                            <div class="d-flex justify-space-between align-center">
                                                <MudText Typo="Typo.subtitle2" Class="font-weight-bold">
                                                    @request.RequesterName
                                                </MudText>
                                                <MudChip T="string" Size="Size.Small"
                                                Color="@GetStatusColor(request.Status)"
                                                Variant="Variant.Filled">
                                                    @GetStatusText(request.Status)
                                                </MudChip>
                                            </div>
                                            <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mt-1">
                                                @Localizer["RequestedOn"]: @request.RequestedDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                                            </MudText>
                                            @if (!string.IsNullOrEmpty(request.CommentsJson))
                                            {
                                                <MudText Typo="Typo.caption" Class="mt-2" Style="font-style: italic;">
                                                    "@request.CommentsJson"
                                                </MudText>
                                            }
                                        </div>
                                    </MudListItem>
                                }
                            </MudList>
                        }
                    </MudPaper>
                </MudItem>
                <!-- Right Panel - Notes Display and Actions -->
                <MudItem xs="12" md="7" lg="8">
                    @if (SelectedRequest != null)
                    {
                        <MudPaper Class="pa-4" Elevation="2">
                            <!-- Request Details Header -->
                            <div class="request-details-header mb-4">
                                <div class="d-flex justify-space-between align-center mb-3">
                                    <div>
                                        <MudText Typo="Typo.h6">
                                            @Localizer["ReviewRequest"] - @SelectedRequest.RequesterName
                                        </MudText>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                                            @Localizer["RequestedOn"]: @SelectedRequest.RequestedDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                                        </MudText>
                                    </div>
                                    <MudChip T="string" Color="@GetStatusColor(SelectedRequest.Status)" Variant="Variant.Filled">
                                        @GetStatusText(SelectedRequest.Status)
                                    </MudChip>
                                </div>

                                @if (!string.IsNullOrEmpty(SelectedRequest.CommentsJson))
                                {
                                    <MudAlert Severity="Severity.Info" Class="mb-3">
                                        <strong>@Localizer["RequestComment"]:</strong> @SelectedRequest.CommentsJson
                                    </MudAlert>
                                }
                            </div>

                            <!-- Progress Notes Display -->
                            <div class="notes-display mb-4">
                                <MudText Typo="Typo.h6" Class="mb-3">
                                    <MudIcon Icon="@Icons.Material.Filled.Description" Class="mr-2" />
                                    @Localizer["ProgressNotes"]
                                </MudText>

                                <!-- Notes content will be loaded here -->
                                <MudPaper Class="notes-content pa-4" Elevation="1" Style="min-height: 300px; background-color: #fafafa;">
                                    @if (IsLoadingNotes)
                                    {
                                        <div class="d-flex justify-center align-center" style="height: 200px;">
                                            <MudProgressCircular Size="Size.Medium" Indeterminate="true" />
                                        </div>
                                    }
                                    else if (!string.IsNullOrEmpty(NotesContent))
                                    {
                                        <div class="notes-text">
                                            @((MarkupString)NotesContent)
                                        </div>
                                    }
                                    else
                                    {
                                        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="text-center">
                                            @Localizer["NoNotesAvailable"]
                                        </MudText>
                                    }
                                </MudPaper>
                            </div>

                            <!-- Comments Section -->
                            <div class="comments-section mb-4">
                                <MudText Typo="Typo.h6" Class="mb-3">
                                    <MudIcon Icon="@Icons.Material.Filled.Comment" Class="mr-2" />
                                    @Localizer["Comments"] (@Comments.Count())
                                </MudText>

                                @if (Comments.Any())
                                {
                                    <div class="comments-list mb-3">
                                        @foreach (var comment in Comments.OrderBy(c => c.CommentDate))
                                        {
                                            <MudCard Class="mb-2" Elevation="1">
                                                <MudCardContent Class="pa-3">
                                                    <div class="d-flex justify-space-between align-center mb-2">
                                                        <div class="d-flex align-center">
                                                            <MudAvatar Size="Size.Small" Color="Color.Primary" Class="mr-2">
                                                                @comment.CommenterName.Substring(0, 1).ToUpper()
                                                            </MudAvatar>
                                                            <div>
                                                                <MudText Typo="Typo.subtitle2">@comment.CommenterName</MudText>
                                                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                                    @comment.CommentDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                                                                </MudText>
                                                            </div>
                                                        </div>
                                                        @if (!comment.IsResolved)
                                                        {
                                                            <MudButton Size="Size.Small"
                                                            Variant="Variant.Text"
                                                            Color="Color.Success"
                                                            OnClick="@(() => ResolveComment(comment.Id))"
                                                            StartIcon="@Icons.Material.Filled.Check">
                                                                @Localizer["Resolve"]
                                                            </MudButton>
                                                        }
                                                        else
                                                        {
                                                            <MudChip T="string" Size="Size.Small" Color="Color.Success" Variant="Variant.Text">
                                                                @Localizer["Resolved"]
                                                            </MudChip>
                                                        }
                                                    </div>
                                                    <MudText Typo="Typo.body2">@comment.Comment</MudText>
                                                    @if (comment.IsResolved)
                                                    {
                                                        <MudText Typo="Typo.caption" Color="Color.Success" Class="mt-2">
                                                            @Localizer["ResolvedBy"] @comment.ResolvedByName @Localizer["On"] @comment.ResolvedDate?.ToString("MMM dd, yyyy")
                                                        </MudText>
                                                    }
                                                </MudCardContent>
                                            </MudCard>
                                        }
                                    </div>
                                }

                                <!-- Add Comment -->
                                <MudTextField @bind-Value="NewComment"
                                Label="@Localizer["AddComment"]"
                                Variant="Variant.Outlined"
                                Lines="3"
                                Class="mb-3" />
                            </div>

                            <!-- Action Buttons -->
                            <div class="action-buttons">
                                <div class="d-flex justify-space-between">
                                    <div class="d-flex gap-3">
                                        <MudButton Variant="Variant.Filled"
                                        Color="Color.Primary"
                                        StartIcon="@Icons.Material.Filled.Comment"
                                        OnClick="AddComment"
                                        Disabled="@(string.IsNullOrWhiteSpace(NewComment) || IsProcessing)">
                                            @Localizer["Comment"]
                                        </MudButton>

                                        <MudButton Variant="Variant.Filled"
                                        Color="Color.Success"
                                        StartIcon="@Icons.Material.Filled.CheckCircle"
                                        OnClick="CosignDocument"
                                        Disabled="@(HasUnresolvedComments || IsProcessing)">
                                            @Localizer["Cosign"]
                                        </MudButton>
                                    </div>

                                    <MudButton Variant="Variant.Outlined"
                                    Color="Color.Error"
                                    StartIcon="@Icons.Material.Filled.Cancel"
                                    OnClick="CancelRequest"
                                    Disabled="@IsProcessing">
                                        @Localizer["Cancel"]
                                    </MudButton>
                                </div>

                                @if (HasUnresolvedComments)
                                {
                                    <MudAlert Severity="Severity.Warning" Class="mt-3">
                                        @Localizer["ResolveCommentsBeforeCosigning"]
                                    </MudAlert>
                                }

                                @if (IsProcessing)
                                {
                                    <div class="d-flex align-center mt-3">
                                        <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                        <MudText Typo="Typo.body2">@Localizer["Processing"]...</MudText>
                                    </div>
                                }
                            </div>
                        </MudPaper>
                    }
                    else
                    {
                        <MudPaper Class="pa-8 text-center" Elevation="2">
                            <MudIcon Icon="@Icons.Material.Outlined.TouchApp"
                            Size="Size.Large"
                            Color="Color.Secondary"
                            Class="mb-4" />
                            <MudText Typo="Typo.h6" Color="Color.Secondary">
                                @Localizer["SelectRequestToReview"]
                            </MudText>
                            <MudText Typo="Typo.body2" Color="Color.Secondary">
                                @Localizer["ChooseRequestFromList"]
                            </MudText>
                        </MudPaper>
                    }
                </MudItem>
            </MudGrid>
        }
    </MudContainer>
</div>





@code {
    private bool IsLoading = true;
    private bool IsLoadingNotes = false;
    private bool IsProcessing = false;
    private bool HasUnresolvedComments => Comments.Any(c => !c.IsResolved);
    [Inject] private UserContext userContext { get; set; }
    private bool Subscription = false;

    private IEnumerable<CosigningRequest> PendingRequests = new List<CosigningRequest>();
    private IEnumerable<CosigningComment> Comments = new List<CosigningComment>();
    private CosigningRequest SelectedRequest;
    private string NotesContent = "";
    private string NewComment = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadPendingRequests();
        IsLoading = false;
    }

    private async Task LoadPendingRequests()
    {
        try
        {
            if (Guid.TryParse(CurrentUser.id, out var userId))
            {
                PendingRequests = await CosigningRequestService.GetByReviewerIdAsync(
                    userId,
                    userContext.ActiveUserOrganizationID,
                    Subscription);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading pending requests");
            Snackbar.Add(Localizer["ErrorLoadingRequests"], Severity.Error);
        }
    }

    private async Task SelectRequest(CosigningRequest request)
    {
        SelectedRequest = request;
        await LoadRequestDetails();
    }

    private async Task LoadRequestDetails()
    {
        if (SelectedRequest == null) return;

        try
        {
            IsLoadingNotes = true;

            // Load comments from the request
            Comments = CommentHelper.GetComments(SelectedRequest.CommentsJson);

            // Load actual notes content from the record
            var record = await ProgressNotesService.GetRecordByIdAsync(
                SelectedRequest.RecordId,
                userContext.ActiveUserOrganizationID,
                Subscription);

            if (record != null)
            {
                NotesContent = record.Notes ?? "No notes available";
            }
            else
            {
                NotesContent = "Notes not found";
            }

            IsLoadingNotes = false;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading request details for {RequestId}", SelectedRequest.Id);
            Snackbar.Add(Localizer["ErrorLoadingDetails"], Severity.Error);
            IsLoadingNotes = false;
        }
    }

    private async Task AddComment()
    {
        if (string.IsNullOrWhiteSpace(NewComment) || SelectedRequest == null) return;

        try
        {
            IsProcessing = true;

            await CosigningRequestService.AddCommentAsync(
                SelectedRequest.Id,
                Guid.Parse(CurrentUser.id),
                CurrentUser.givenName,
                NewComment,
                userContext.ActiveUserOrganizationID,
                Subscription);

            NewComment = "";
            await LoadRequestDetails(); // Refresh comments

            Snackbar.Add(Localizer["CommentAdded"], Severity.Success);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error adding comment to request {RequestId}", SelectedRequest.Id);
            Snackbar.Add(Localizer["ErrorAddingComment"], Severity.Error);
        }
        finally
        {
            IsProcessing = false;
        }
    }

    private async Task ResolveComment(Guid commentId)
    {
        try
        {
            IsProcessing = true;

            await CosigningRequestService.ResolveCommentAsync(
                SelectedRequest.Id,
                commentId,
                Guid.Parse(CurrentUser.id),
                CurrentUser.givenName,
                userContext.ActiveUserOrganizationID,
                Subscription);

            await LoadRequestDetails(); // Refresh comments

            Snackbar.Add(Localizer["CommentResolved"], Severity.Success);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error resolving comment {CommentId}", commentId);
            Snackbar.Add(Localizer["ErrorResolvingComment"], Severity.Error);
        }
        finally
        {
            IsProcessing = false;
        }
    }

    private async Task CosignDocument()
    {
        if (SelectedRequest == null || HasUnresolvedComments) return;

        try
        {
            IsProcessing = true;

            await CosigningRequestService.ApproveRequestAsync(
                SelectedRequest.Id,
                Guid.Parse(CurrentUser.id),
                CurrentUser.givenName,
                userContext.ActiveUserOrganizationID,
                Subscription);

            Snackbar.Add(Localizer["DocumentCosigned"], Severity.Success);

            // Remove from pending list and clear selection
            PendingRequests = PendingRequests.Where(r => r.Id != SelectedRequest.Id);
            SelectedRequest = null;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error cosigning document for request {RequestId}", SelectedRequest.Id);
            Snackbar.Add(Localizer["ErrorCosigningDocument"], Severity.Error);
        }
        finally
        {
            IsProcessing = false;
        }
    }

    private async Task CancelRequest()
    {
        if (SelectedRequest == null) return;

        try
        {
            IsProcessing = true;

            await CosigningRequestService.CancelRequestAsync(
                SelectedRequest.Id,
                userContext.ActiveUserOrganizationID,
                Subscription);

            Snackbar.Add(Localizer["RequestCancelled"], Severity.Info);

            // Remove from pending list and clear selection
            PendingRequests = PendingRequests.Where(r => r.Id != SelectedRequest.Id);
            SelectedRequest = null;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error cancelling request {RequestId}", SelectedRequest.Id);
            Snackbar.Add(Localizer["ErrorCancellingRequest"], Severity.Error);
        }
        finally
        {
            IsProcessing = false;
        }
    }

    private Color GetStatusColor(CosigningRequestStatus status)
    {
        return status switch
        {
            CosigningRequestStatus.Pending => Color.Warning,            
            CosigningRequestStatus.Approved => Color.Success,
            CosigningRequestStatus.ChangesRequested => Color.Primary,
            
            _ => Color.Default
        };
    }

    private string GetStatusText(CosigningRequestStatus status)
    {
        return status switch
        {
            CosigningRequestStatus.Pending => Localizer["Requested"],           
            CosigningRequestStatus.Approved => Localizer["Cosigned"],
            CosigningRequestStatus.ChangesRequested => Localizer["Commented"],
            
            _ => status.ToString()
        };
    }

    private string GetSampleNotesContent()
    {
        return @"
            <div style='font-family: Arial, sans-serif; line-height: 1.6;'>
                <h4>SOAP Notes</h4>
                <p><strong>Subjective:</strong><br/>
                Patient reports mild headache and fatigue for the past 2 days. No fever, nausea, or visual disturbances.
                Sleep pattern has been irregular due to work stress.</p>

                <p><strong>Objective:</strong><br/>
                Vital Signs: BP 120/80, HR 72, Temp 98.6°F, RR 16<br/>
                General: Alert and oriented, appears tired<br/>
                HEENT: No signs of sinus tenderness, pupils equal and reactive<br/>
                Neck: No lymphadenopathy or neck stiffness</p>

                <p><strong>Assessment:</strong><br/>
                Tension headache, likely stress-related<br/>
                Mild fatigue secondary to poor sleep hygiene</p>

                <p><strong>Plan:</strong><br/>
                1. Prescribe acetaminophen 500mg BID PRN for headache<br/>
                2. Recommend stress management techniques<br/>
                3. Sleep hygiene counseling<br/>
                4. Follow-up in 1 week if symptoms persist<br/>
                5. Return immediately if severe headache, fever, or neurological symptoms develop</p>
            </div>";
    }
}