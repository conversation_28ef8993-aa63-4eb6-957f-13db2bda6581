@page "/my-requests"
@using Microsoft.Extensions.Localization
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using MudBlazor
@using Microsoft.AspNetCore.Authorization
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@attribute [Authorize]
@layout Admin

<PageTitle>@Localizer["MyRequests"]</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-6">
    <!-- Header Section -->
    <MudStack Row AlignItems="AlignItems.Center" Spacing="3" Class="mb-6">
        <MudIcon Icon="@Icons.Material.Filled.Assignment" Size="Size.Large" Color="Color.Primary" />
        <MudStack Spacing="1">
            <MudText Typo="Typo.h4" Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-weight: 600; color: #333;">
                @Localizer["MyRequests"]
            </MudText>
            <MudText Typo="Typo.body1" Style="color: #666;">
                @Localizer["MyRequestsDescription"]
            </MudText>
        </MudStack>
    </MudStack>

    @if (_isLoading)
    {
        <MudProgressCircular Indeterminate="true" Size="Size.Large" Class="ma-4" />
    }
    else if (!_myRequests.Any())
    {
        <MudAlert Severity="Severity.Info" Class="ma-4">
            @Localizer["NoRequestsMade"]
        </MudAlert>
    }
    else
    {
        <MudDataGrid Items="_myRequests" 
                     Filterable="true" 
                     SortMode="SortMode.Multiple" 
                     Groupable="false"
                     Class="my-requests-grid"
                     Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
            <Columns>
                <PropertyColumn Property="x => x.PatientName" Title="@Localizer["PatientName"]" />
                <PropertyColumn Property="x => x.RecordDate" Title="@Localizer["RecordDate"]" Format="MM/dd/yyyy" />
                <PropertyColumn Property="x => x.ReviewerName" Title="@Localizer["RequestedProvider"]" />
                <TemplateColumn Title="@Localizer["Status"]">
                    <CellTemplate>
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudChip T="string"
                                     Color="@GetStatusColor(context.Item.Status)"
                                     Size="Size.Small"
                                     Icon="@GetStatusIcon(context.Item.Status)"
                                     Variant="Variant.Filled"
                                     Style="font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">
                                @GetStatusText(context.Item.Status)
                            </MudChip>
                            @if (context.Item.Status == CosigningRequestStatus.ChangesRequested)
                            {
                                <MudTooltip Text="@Localizer["ClickToViewComments"]">
                                    <MudIconButton Icon="@Icons.Material.Filled.Comment"
                                                   Color="Color.Info"
                                                   Size="Size.Small"
                                                   OnClick="@(() => ViewComments(context.Item))" />
                                </MudTooltip>
                            }
                        </MudStack>
                    </CellTemplate>
                </TemplateColumn>
                <TemplateColumn Title="@Localizer["ActionDate"]">
                    <CellTemplate>
                        @if (context.Item.ReviewedDate.HasValue)
                        {
                            <MudText Typo="Typo.body2">
                                @GetActionText(context.Item.Status): @context.Item.ReviewedDate.Value.ToString("MM/dd/yyyy HH:mm")
                            </MudText>
                        }
                        else
                        {
                            <MudText Typo="Typo.body2" Class="text-muted">
                                @Localizer["Pending"]
                            </MudText>
                        }
                    </CellTemplate>
                </TemplateColumn>
                <TemplateColumn Title="@Localizer["Actions"]">
                    <CellTemplate>
                        @if (context.Item.Status == CosigningRequestStatus.Approved)
                        {
                            <MudStack Row Spacing="1">
                                <MudButton Variant="Variant.Filled"
                                           Color="Color.Success"
                                           Size="Size.Small"
                                           OnClick="@(() => NavigateToNotes(context.Item))"
                                           StartIcon="@Icons.Material.Filled.Lock"
                                           Style="font-weight: 600;">
                                    @Localizer["LockNotes"]
                                </MudButton>
                                <MudTooltip Text="@Localizer["ViewNotes"]">
                                    <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                                   Color="Color.Info"
                                                   Size="Size.Small"
                                                   OnClick="@(() => NavigateToNotes(context.Item))" />
                                </MudTooltip>
                            </MudStack>
                        }
                        else if (context.Item.Status == CosigningRequestStatus.ChangesRequested)
                        {
                            <MudStack Row Spacing="1">
                                <MudButton Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           Size="Size.Small"
                                           OnClick="@(() => NavigateToNotes(context.Item))"
                                           StartIcon="@Icons.Material.Filled.Edit"
                                           Style="font-weight: 600;">
                                    @Localizer["MakeChanges"]
                                </MudButton>
                                <MudTooltip Text="@Localizer["ViewComments"]">
                                    <MudIconButton Icon="@Icons.Material.Filled.Comment"
                                                   Color="Color.Warning"
                                                   Size="Size.Small"
                                                   OnClick="@(() => ViewComments(context.Item))" />
                                </MudTooltip>
                            </MudStack>
                        }
                        else if (context.Item.Status == CosigningRequestStatus.Pending)
                        {
                            <MudStack Row Spacing="1" AlignItems="AlignItems.Center">
                                <MudProgressCircular Size="Size.Small" Indeterminate="true" Color="Color.Warning" />
                                <MudText Typo="Typo.body2" Style="color: #ff9800; font-weight: 500;">
                                    @Localizer["WaitingForReview"]
                                </MudText>
                                <MudTooltip Text="@Localizer["ViewNotes"]">
                                    <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                                   Color="Color.Info"
                                                   Size="Size.Small"
                                                   OnClick="@(() => NavigateToNotes(context.Item))" />
                                </MudTooltip>
                            </MudStack>
                        }
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
        </MudDataGrid>
    }
</MudContainer>

<!-- Comments Dialog -->
<MudDialog @bind-IsVisible="_showCommentsDialog" Options="_dialogOptions">
    <TitleContent>
        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
            <MudIcon Icon="@Icons.Material.Filled.Comment" Color="Color.Primary" Size="Size.Medium" />
            <MudText Typo="Typo.h6" Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-weight: 600;">
                @Localizer["ReviewComments"]
            </MudText>
            @if (_selectedRequest != null)
            {
                <MudChip T="string" Color="Color.Info" Size="Size.Small" Style="margin-left: auto;">
                    @_selectedRequest.PatientName
                </MudChip>
            }
        </MudStack>
    </TitleContent>
    <DialogContent>
        @if (_selectedRequest != null)
        {
            <MudPaper Class="pa-4" Style="max-height: 500px; overflow-y: auto; background: #f8f9fa; border-radius: 8px;">
                @if (CommentHelper.GetComments(_selectedRequest.CommentsJson).Any())
                {
                    @foreach (var comment in CommentHelper.GetComments(_selectedRequest.CommentsJson).OrderBy(c => c.CommentDate))
                    {
                        <div class="github-comment-style" style="border: 1px solid #e1e4e8; border-radius: 8px; background: white; margin-bottom: 16px; overflow: hidden;">
                            <!-- Comment Header -->
                            <div style="background: #f6f8fa; border-bottom: 1px solid #e1e4e8; padding: 12px 16px;">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudAvatar Size="Size.Small" Style="background: #1976d2; color: white; font-weight: 600;">
                                        @comment.CommenterName.Substring(0, 1).ToUpper()
                                    </MudAvatar>
                                    <MudText Typo="Typo.body2" Style="font-weight: 600; color: #24292e;">
                                        @comment.CommenterName
                                    </MudText>
                                    <MudText Typo="Typo.caption" Style="color: #586069;">
                                        commented @comment.CommentDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                                    </MudText>
                                    <MudSpacer />
                                    @if (comment.IsResolved)
                                    {
                                        <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.CheckCircle">
                                            @Localizer["Resolved"]
                                        </MudChip>
                                    }
                                    else
                                    {
                                        <MudChip T="string" Color="Color.Warning" Size="Size.Small" Icon="@Icons.Material.Filled.Schedule">
                                            @Localizer["NeedsAttention"]
                                        </MudChip>
                                    }
                                </MudStack>
                            </div>

                            <!-- Comment Body -->
                            <div style="padding: 16px;">
                                <MudText Typo="Typo.body2" Style="line-height: 1.6; color: #24292e;">
                                    @comment.Comment
                                </MudText>
                                @if (comment.IsResolved && comment.ResolvedDate.HasValue)
                                {
                                    <MudAlert Severity="Severity.Success" Dense="true" Style="margin-top: 12px;">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" />
                                            <MudText Typo="Typo.caption">
                                                @Localizer["ResolvedOn"] @comment.ResolvedDate.Value.ToString("MMM dd, yyyy 'at' h:mm tt")
                                            </MudText>
                                        </MudStack>
                                    </MudAlert>
                                }
                            </div>
                        </div>
                    }
                }
                else
                {
                    <MudAlert Severity="Severity.Info" Style="text-align: center;">
                        <MudStack AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.CommentBank" Size="Size.Large" />
                            <MudText Typo="Typo.body1">@Localizer["NoComments"]</MudText>
                        </MudStack>
                    </MudAlert>
                }
            </MudPaper>
        }
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Outlined"
                   Color="Color.Secondary"
                   OnClick="CloseCommentsDialog"
                   StartIcon="@Icons.Material.Filled.Close">
            @Localizer["Close"]
        </MudButton>

        @if (_selectedRequest != null)
        {
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       OnClick="@(() => NavigateToNotes(_selectedRequest))"
                       StartIcon="@Icons.Material.Filled.Edit"
                       Style="font-weight: 600;">
                @Localizer["MakeChanges"]
            </MudButton>
        }
    </DialogActions>
</MudDialog>

<style>
    .my-requests-grid {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .text-muted {
        color: #6c757d;
    }

    .github-comment-style {
        transition: box-shadow 0.2s ease;
    }

    .github-comment-style:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .mud-table-row:hover {
        background-color: #f8f9fa !important;
    }

    .mud-chip {
        font-weight: 600;
    }

    .mud-button {
        text-transform: none;
        border-radius: 6px;
    }

    .mud-progress-circular {
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
