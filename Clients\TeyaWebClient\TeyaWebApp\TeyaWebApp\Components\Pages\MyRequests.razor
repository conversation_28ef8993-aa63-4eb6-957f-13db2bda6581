@page "/my-requests"
@using Microsoft.Extensions.Localization
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using MudBlazor
@using Microsoft.AspNetCore.Authorization
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@attribute [Authorize]
@layout Admin
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer

<PageTitle>@Localizer["MyRequests"]</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-6">
    <!-- Header Section -->
    <MudStack Row AlignItems="AlignItems.Center" Spacing="3" Class="mb-6">
        <MudIcon Icon="@Icons.Material.Filled.Assignment" Size="Size.Large" Color="Color.Primary" />
        <MudStack Spacing="1">
            <MudText Typo="Typo.h4" Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-weight: 600; color: #333;">
                @Localizer["MyRequests"]
            </MudText>
            <MudText Typo="Typo.body1" Style="color: #666;">
                @Localizer["MyRequestsDescription"]
            </MudText>
        </MudStack>
    </MudStack>

    @if (_isLoading)
    {
        <MudProgressCircular Indeterminate="true" Size="Size.Large" Class="ma-4" />
    }
    else if (!_myRequests.Any())
    {
        <MudAlert Severity="Severity.Info" Class="ma-4">
            @Localizer["NoRequestsMade"]
        </MudAlert>
    }
    else
    {
        <MudDataGrid Items="_myRequests" 
                     Filterable="true" 
                     SortMode="SortMode.Multiple" 
                     Groupable="false"
                     Class="my-requests-grid"
                     Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
            <Columns>
                <PropertyColumn Property="x => x.PatientName" Title="@Localizer["PatientName"]" />
                <PropertyColumn Property="x => x.RecordDate" Title="@Localizer["RecordDate"]" Format="MM/dd/yyyy" />
                <PropertyColumn Property="x => x.ReviewerName" Title="@Localizer["RequestedProvider"]" />
                <TemplateColumn Title="@Localizer["Status"]">
                    <CellTemplate>
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@GetStatusIcon(context.Item.Status)"
                                     Color="@GetStatusColor(context.Item.Status)"
                                     Size="Size.Small" />
                            <MudChip T="string"
                                     Color="@GetStatusColor(context.Item.Status)"
                                     Size="Size.Small"
                                     Icon="@GetStatusIcon(context.Item.Status)">
                                @GetStatusText(context.Item.Status)
                            </MudChip>
                        </MudStack>
                    </CellTemplate>
                </TemplateColumn>
                <TemplateColumn Title="@Localizer["ActionDate"]">
                    <CellTemplate>
                        @if (context.Item.ReviewedDate.HasValue)
                        {
                            <MudText Typo="Typo.body2">
                                @GetActionText(context.Item.Status): @context.Item.ReviewedDate.Value.ToString("MM/dd/yyyy HH:mm")
                            </MudText>
                        }
                        else
                        {
                            <MudText Typo="Typo.body2" Class="text-muted">
                                @Localizer["Pending"]
                            </MudText>
                        }
                    </CellTemplate>
                </TemplateColumn>
                <TemplateColumn Title="@Localizer["Actions"]">
                    <CellTemplate>
                        @if (context.Item.Status == CosigningRequestStatus.Approved)
                        {
                            <MudButton Variant="Variant.Filled" 
                                       Color="Color.Success" 
                                       Size="Size.Small"
                                       OnClick="@(() => NavigateToNotes(context.Item))"
                                       StartIcon="@Icons.Material.Filled.Lock">
                                @Localizer["LockNotes"]
                            </MudButton>
                        }
                        else if (context.Item.Status == CosigningRequestStatus.ChangesRequested)
                        {
                            <MudStack Row Spacing="1">
                                <MudButton Variant="Variant.Outlined" 
                                           Color="Color.Info" 
                                           Size="Size.Small"
                                           OnClick="@(() => ViewComments(context.Item))"
                                           StartIcon="@Icons.Material.Filled.Comment">
                                    @Localizer["ViewComments"]
                                </MudButton>
                                <MudButton Variant="Variant.Filled" 
                                           Color="Color.Primary" 
                                           Size="Size.Small"
                                           OnClick="@(() => NavigateToNotes(context.Item))"
                                           StartIcon="@Icons.Material.Filled.Edit">
                                    @Localizer["MakeChanges"]
                                </MudButton>
                            </MudStack>
                        }
                        else
                        {
                            <MudText Typo="Typo.body2" Class="text-muted">
                                @Localizer["WaitingForReview"]
                            </MudText>
                        }
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
        </MudDataGrid>
    }
</MudContainer>

<!-- Comments Dialog -->
<MudDialog @bind-IsVisible="_showCommentsDialog" Options="_dialogOptions">
    <DialogContent>
        @if (_selectedRequest != null)
        {
            <MudText Typo="Typo.h5" Class="mb-4" Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                @Localizer["ReviewComments"] - @_selectedRequest.PatientName
            </MudText>

            <MudPaper Class="pa-4" Style="max-height: 500px; overflow-y: auto;">
                @if (CommentHelper.GetComments(_selectedRequest.CommentsJson).Any())
                {
                    @foreach (var comment in CommentHelper.GetComments(_selectedRequest.CommentsJson).OrderBy(c => c.CommentDate))
                    {
                        <MudCard Class="mb-3" Elevation="2">
                            <MudCardContent Class="pa-4">
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Start" Class="mb-2">
                                    <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                        @comment.CommenterName
                                    </MudText>
                                    <MudText Typo="Typo.caption" Class="text-muted">
                                        @comment.CommentDate.ToString("MM/dd/yyyy HH:mm")
                                    </MudText>
                                </MudStack>
                                
                                <MudText Typo="Typo.body1" Class="mb-2" Style="line-height: 1.6;">
                                    @comment.Comment
                                </MudText>
                                
                                @if (comment.IsResolved)
                                {
                                    <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.CheckCircle">
                                        @Localizer["Resolved"] @if (comment.ResolvedDate.HasValue) { <text>- @comment.ResolvedDate.Value.ToString("MM/dd/yyyy")</text> }
                                    </MudChip>
                                }
                                else
                                {
                                    <MudChip T="string" Color="Color.Warning" Size="Size.Small" Icon="@Icons.Material.Filled.Schedule">
                                        @Localizer["NeedsAttention"]
                                    </MudChip>
                                }
                            </MudCardContent>
                        </MudCard>
                    }
                }
                else
                {
                    <MudText Typo="Typo.body1" Class="text-center pa-4">
                        @Localizer["NoComments"]
                    </MudText>
                }
            </MudPaper>

            <MudStack Row Justify="Justify.SpaceBetween" Class="mt-4">
                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Secondary"
                           OnClick="CloseCommentsDialog">
                    @Localizer["Close"]
                </MudButton>
                
                <MudButton Variant="Variant.Filled" 
                           Color="Color.Primary"
                           OnClick="@(() => NavigateToNotes(_selectedRequest))"
                           StartIcon="@Icons.Material.Filled.Edit">
                    @Localizer["MakeChanges"]
                </MudButton>
            </MudStack>
        }
    </DialogContent>
</MudDialog>

<style>
    .my-requests-grid {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .text-muted {
        color: #6c757d;
    }
</style>
