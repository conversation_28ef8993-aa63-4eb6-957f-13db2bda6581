using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.TeyaAIScribeResources;

namespace TeyaWebApp.Components.Pages
{
    public partial class MyRequests : ComponentBase, IDisposable
    {
        [Inject] private ICosigningRequestService CosigningRequestService { get; set; }
        [Inject] private ICosigningCommentHelper CommentHelper { get; set; }
        [Inject] private IProgressNotesService ProgressNotesService { get; set; }
        [Inject] private ICosigningStateService CosigningStateService { get; set; }
        [Inject] private ActiveUser CurrentUser { get; set; }
        [Inject] private ILogger<MyRequests> Logger { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private NavigationManager Navigation { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        [Inject] private IStringLocalizer<TeyaAIScribeStrings> Localizer { get; set; }

        private List<CosigningRequest> _myRequests = new();
        private CosigningRequest? _selectedRequest;
        private bool _isLoading = true;
        private bool _showCommentsDialog = false;
        private bool Subscription = false;
        private Timer? _refreshTimer;

        private readonly DialogOptions _dialogOptions = new()
        {
            MaxWidth = MaxWidth.Large,
            FullWidth = true,
            CloseButton = true,            
        };

        protected override async Task OnInitializedAsync()
        {
            await LoadMyRequests();

            // Subscribe to state changes for real-time updates
            CosigningStateService.RequestUpdated += OnRequestUpdated;
            CosigningStateService.RequestApproved += OnRequestApproved;
            CosigningStateService.RequestChangesRequested += OnRequestChangesRequested;
            CosigningStateService.RequestCountsChanged += OnRequestCountsChanged;

            // Set up timer to refresh every 30 seconds
            _refreshTimer = new Timer(async _ => await RefreshRequests(), null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        private async Task LoadMyRequests()
        {
            try
            {
                _isLoading = true;
                
                if (Guid.TryParse(CurrentUser.id, out var userId))
                {
                    var requests = await CosigningRequestService.GetByRequesterIdAsync(
                        userId, 
                        UserContext.ActiveUserOrganizationID, 
                        Subscription);
                    
                    _myRequests = requests.OrderByDescending(r => r.RequestedDate).ToList();
                    
                    // Load patient information for each request
                    foreach (var request in _myRequests)
                    {
                        await LoadPatientInfo(request);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading my requests");
                Snackbar.Add(Localizer["ErrorLoadingRequests"], Severity.Error);
            }
            finally
            {
                _isLoading = false;
                StateHasChanged();
            }
        }

        private async Task LoadPatientInfo(CosigningRequest request)
        {
            try
            {
                // Load the progress note to get patient information
                var notes = await ProgressNotesService.GetRecordByIdAsync(
                    request.RecordId, 
                    UserContext.ActiveUserOrganizationID,
                    Subscription);
                
                if (notes != null)
                {
                    request.PatientName = notes.PatientName ?? "Unknown Patient";
                    request.RecordDate = notes.DateTime;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading patient info for request {RequestId}", request.Id);
                request.PatientName = "Unknown Patient";
                request.RecordDate = DateTime.Now;
            }
        }

        private Color GetStatusColor(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Color.Warning,
                CosigningRequestStatus.Approved => Color.Success,
                CosigningRequestStatus.ChangesRequested => Color.Error,
                _ => Color.Default
            };
        }

        private string GetStatusText(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Localizer["Pending"],
                CosigningRequestStatus.Approved => Localizer["Approved"],
                CosigningRequestStatus.ChangesRequested => Localizer["ChangesRequested"],
                _ => status.ToString()
            };
        }

        private string GetStatusIcon(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Icons.Material.Filled.Schedule,
                CosigningRequestStatus.Approved => Icons.Material.Filled.CheckCircle,
                CosigningRequestStatus.ChangesRequested => Icons.Material.Filled.Comment,
                _ => Icons.Material.Filled.Info
            };
        }

        private string GetActionText(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Approved => Localizer["Approved"],
                CosigningRequestStatus.ChangesRequested => Localizer["Commented"],
                _ => string.Empty
            };
        }

        private void ViewComments(CosigningRequest request)
        {
            _selectedRequest = request;
            _showCommentsDialog = true;
        }

        private void CloseCommentsDialog()
        {
            _showCommentsDialog = false;
            _selectedRequest = null;
        }

        private void NavigateToNotes(CosigningRequest request)
        {
            // Navigate to the notes page with the specific record
            var notesUrl = $"/soapnotes?pcpId={CurrentUser.id}&sub={Subscription}&recordId={request.RecordId}";
            Navigation.NavigateTo(notesUrl);
        }

        private async Task RefreshRequests()
        {
            try
            {
                await LoadMyRequests();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error refreshing my requests");
            }
        }

        private async void OnRequestUpdated(CosigningRequest updatedRequest)
        {
            try
            {
                var existingRequest = _myRequests.FirstOrDefault(r => r.Id == updatedRequest.Id);
                if (existingRequest != null)
                {
                    var index = _myRequests.IndexOf(existingRequest);
                    _myRequests[index] = updatedRequest;
                    await InvokeAsync(StateHasChanged);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling request update");
            }
        }

        private async void OnRequestApproved(Guid requestId)
        {
            try
            {
                var request = _myRequests.FirstOrDefault(r => r.Id == requestId);
                if (request != null)
                {
                    request.Status = CosigningRequestStatus.Approved;
                    request.ReviewedDate = DateTime.UtcNow;
                    await InvokeAsync(StateHasChanged);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling request approval");
            }
        }

        private async void OnRequestChangesRequested(Guid requestId)
        {
            try
            {
                var request = _myRequests.FirstOrDefault(r => r.Id == requestId);
                if (request != null)
                {
                    request.Status = CosigningRequestStatus.ChangesRequested;
                    request.ReviewedDate = DateTime.UtcNow;
                    await InvokeAsync(StateHasChanged);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling changes requested");
            }
        }

        private async void OnRequestCountsChanged()
        {
            try
            {
                await RefreshRequests();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling request counts change");
            }
        }

        public void Dispose()
        {
            // Unsubscribe from events
            if (CosigningStateService != null)
            {
                CosigningStateService.RequestUpdated -= OnRequestUpdated;
                CosigningStateService.RequestApproved -= OnRequestApproved;
                CosigningStateService.RequestChangesRequested -= OnRequestChangesRequested;
                CosigningStateService.RequestCountsChanged -= OnRequestCountsChanged;
            }

            // Dispose timer
            _refreshTimer?.Dispose();
        }
    }
}
